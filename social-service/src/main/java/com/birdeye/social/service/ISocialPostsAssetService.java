package com.birdeye.social.service;

import com.birdeye.social.dao.SocialPostsAssetsRepository;
import com.birdeye.social.entities.SocialPostsAssets;
import com.birdeye.social.model.MediaData;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ISocialPostsAssetService {
    List<String> findImageUrlsByIds(List<Integer> ids, String businessNum);

    Map<String, String> getIdVsImageUrlsByIds(List<Integer> ids, String businessNum);

    List<String> findVideoUrlsByIds(List<Integer> ids, String businessNum);

    SocialPostsAssets findById(Integer id);

    String getCompleteCdnUrlFromBaseUrl(String url, String enterpriseNum);

    List<SocialPostsAssets> findAllByImageUrl(List<String> imageUrls);

    List<SocialPostsAssets> findAllByImageUrlAndBucketId(List<String> imageUrls, String bucketId);

    List<SocialPostsAssets> findAllByMediaBucket(List<SocialPostsAssetsRepository.SocialPostMediaBucket> mediaBuckets, boolean isImage);

    void save(SocialPostsAssets asset);

    void save(List<SocialPostsAssets> assets);

    String getCompleteImageUrlFromPostAsset(SocialPostsAssets post, String businessNum);

    String getCompleteVideoUrlFromPostAsset(SocialPostsAssets post, String businessNum);

    void flush();

    void saveAndFlush(SocialPostsAssets postAsset);

    List<SocialPostsAssets> findByIds(Set<Integer> ids);

    String getBaseCdnUrl();

    List<SocialPostsAssets> findAllByVideoUrl(List<String> socialAssets);

    List<SocialPostsAssets> findAllByVideoUrlAndBucketId(List<String> socialAssets, String bucketId);

    String findThumbnailById(Integer id);

    SocialPostsAssets findByVideoUrl(String videoUrl);

    SocialPostsAssets findByVideoUrlAndBucketId(String videoUrl, String bucketId);

    SocialPostsAssets findByImageUrl(String mediaAssetUrl);

    SocialPostsAssets findByImageUrlAndBucketId(String mediaAssetUrl, String bucketId);

    List<Integer> getAssetIdsForAIImages(List<MediaData> socialAssetsData, String type, String videoThumbnailUrl, Integer businessId);

}
