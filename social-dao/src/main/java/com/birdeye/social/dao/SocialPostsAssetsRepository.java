package com.birdeye.social.dao;

import com.birdeye.social.entities.SocialPostsAssets;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Set;

public interface SocialPostsAssetsRepository extends JpaRepository<SocialPostsAssets, Integer>, JpaSpecificationExecutor<SocialPostsAssets> {

	public SocialPostsAssets findById(Integer id);
	
	@Query("select s from SocialPostsAssets s where s.id in :ids")
	public List<SocialPostsAssets> findByIds(@Param("ids") Set<Integer> ids);
	
	@Query("select s from SocialPostsAssets s where s.imageUrl in (:imageUrls)")
	public List<SocialPostsAssets> findAllByImageUrl(@Param("imageUrls") List<String> imageUrls);

	@Query("select s from SocialPostsAssets s where s.imageUrl in (:imageUrls) and bucketId = :bucketId")
	public List<SocialPostsAssets> findAllByImageUrlAndBucketId(@Param("imageUrls") List<String> imageUrls, @Param("bucketId") String bucketId);
	
	public SocialPostsAssets findByVideoUrl(String videoUrl);

	public SocialPostsAssets findByVideoUrlAndBucketId(String videoUrl, String bucketId);

	public SocialPostsAssets findByImageUrl(String imageUrl);

	public SocialPostsAssets findByImageUrlAndBucketId(String imageUrl, String bucketId);
	
	@Query("Select s.imageUrl from SocialPostsAssets s where s.id in :ids")
	public List<String> findImageUrlsByIds(@Param("ids") List<Integer> ids);

	@Query("Select s.videoUrl from SocialPostsAssets s where s.id in :ids")
	public List<String> findVideoUrlsByIds(@Param("ids") List<Integer> ids);

	@Query("Select s.videoUrl from SocialPostsAssets s where s.id = :id")
	public String findVideoUrlsById(@Param("id") Integer id);

	@Query("select s from SocialPostsAssets s where s.videoUrl in :videoUrls")
	List<SocialPostsAssets> findAllByVideoUrl(@Param("videoUrls") List<String> videoUrls);

	@Query("select s from SocialPostsAssets s where s.videoUrl in :videoUrls and bucketId = :bucketId")
	List<SocialPostsAssets> findAllByVideoUrlAndBucketId(@Param("videoUrls") List<String> videoUrls, @Param("bucketId") String bucketId);

	@Query("select s.videoThumbnail from SocialPostsAssets s where s.id = :id ")
    String findThumbnailById(@Param("id") Integer videoIds);

	@Query("Select s.videoThumbnail from SocialPostsAssets s where s.id = :id")
	public String findVideoThumbnailById(@Param("id") Integer id);

	List<SocialPostAssetsBasicDetails> findByIdIn(List<Integer> ids);

	@Query("Select s from SocialPostsAssets s where s.imageUrl in :urls")
	List<SocialPostsAssets> findByMediaUrls(@Param("urls") List<String> urls);

	default List<SocialPostsAssets> findByMediaBucket(List<SocialPostMediaBucket> mediaBuckets, boolean isImage) {
		return findAll((Root<SocialPostsAssets> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
			Predicate[] predicates = new Predicate[mediaBuckets.size()];
			int i = 0;
			for (SocialPostMediaBucket mediaBucket : mediaBuckets) {
				predicates[i] = cb.and(
						cb.equal(isImage?root.get("imageUrl"):root.get("videoUrl"), mediaBucket.getMediaUrl()),
						cb.equal(root.get("bucketId"), mediaBucket.getBucketId())
				);
				i++;
			}
			return cb.or(predicates);
		});
	}

	public interface SocialPostAssetsBasicDetails{
		Integer getId();
		String getVideoUrl();
		String getImageUrl();
		String getBucketId();
	}


	@Getter
	@Setter
	@AllArgsConstructor
	@NoArgsConstructor
	public class SocialPostMediaBucket {
		private String mediaUrl;
		private String bucketId;
	}

}
